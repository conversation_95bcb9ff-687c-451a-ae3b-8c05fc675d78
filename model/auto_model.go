package model

import (
	"fmt"

	"gorm.io/gorm"
)

// AutoModelConfig represents a logical auto model configuration
type AutoModelConfig struct {
	ID          uint               `gorm:"primaryKey" json:"id"`
	UserID      uint               `gorm:"index;not null" json:"user_id"`
	Name        string             `gorm:"type:varchar(100);not null;index:idx_auto_model_configs_user_name,unique" json:"name"`
	Description string             `gorm:"type:varchar(255)" json:"description"`
	IsActive    bool               `gorm:"default:true" json:"is_active"`
	CreatedAt   int64              `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   int64              `gorm:"autoUpdateTime" json:"updated_at"`
	Mappings    []AutoModelMapping `gorm:"foreignKey:ConfigID" json:"mappings,omitempty"`
}

// AutoModelMapping represents a mapping between a logical auto model and an actual model
type AutoModelMapping struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	ConfigID  uint   `gorm:"not null;index" json:"config_id"`
	ModelName string `gorm:"type:varchar(255);not null" json:"model_name"`
	Priority  int    `gorm:"default:0;index" json:"priority"`
	CreatedAt int64  `gorm:"autoCreateTime" json:"created_at"`
}

// GetAutoModelConfig retrieves an auto model configuration by name and user ID with its mappings
func GetAutoModelConfig(db *gorm.DB, userID uint, name string) (*AutoModelConfig, error) {
	var config AutoModelConfig
	err := db.Preload("Mappings", func(db *gorm.DB) *gorm.DB {
		return db.Order("auto_model_mappings.priority DESC")
	}).Where("user_id = ? AND name = ? AND is_active = ?", userID, name, true).First(&config).Error

	if err != nil {
		return nil, err
	}

	return &config, nil
}

// GetAutoModelConfigByName retrieves an auto model configuration by name and user ID with its mappings (regardless of is_active status)
func GetAutoModelConfigByName(db *gorm.DB, userID uint, name string) (*AutoModelConfig, error) {
	var config AutoModelConfig
	err := db.Preload("Mappings", func(db *gorm.DB) *gorm.DB {
		return db.Order("auto_model_mappings.priority DESC")
	}).Where("user_id = ? AND name = ?", userID, name).First(&config).Error

	if err != nil {
		return nil, err
	}

	return &config, nil
}

// GetMappedModels returns the list of actual model names for a given logical auto model and user ID
// Sorted by priority (descending)
func GetMappedModels(db *gorm.DB, userID uint, autoModelName string) ([]string, error) {
	var mappings []AutoModelMapping

	err := db.Table("auto_model_mappings").
		Joins("JOIN auto_model_configs ON auto_model_mappings.config_id = auto_model_configs.id").
		Where("auto_model_configs.user_id = ? AND auto_model_configs.name = ? AND auto_model_configs.is_active = ?",
			userID, autoModelName, true).
		Order("auto_model_mappings.priority DESC").
		Pluck("auto_model_mappings.model_name", &mappings).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get mapped models: %v", err)
	}

	// If no mappings found, return default models
	if len(mappings) == 0 {
		return []string{"qwen-long-latest", "qwen3-235b-a22b"}, nil
	}

	// Extract model names from mappings
	modelNames := make([]string, 0, len(mappings))
	for _, m := range mappings {
		modelNames = append(modelNames, m.ModelName)
	}

	return modelNames, nil
}

// GetAllAutoModelConfigs returns all auto model configurations for a user with their mappings
func GetAllAutoModelConfigs(db *gorm.DB, userID uint) ([]AutoModelConfig, error) {
	var configs []AutoModelConfig
	err := db.Preload("Mappings", func(db *gorm.DB) *gorm.DB {
		return db.Order("auto_model_mappings.priority DESC")
	}).Where("user_id = ?", userID).Find(&configs).Error

	return configs, err
}

// GetAllAutoModelConfigsAdmin returns all auto model configurations for admin access
func GetAllAutoModelConfigsAdmin(db *gorm.DB) ([]AutoModelConfig, error) {
	var configs []AutoModelConfig
	err := db.Preload("Mappings", func(db *gorm.DB) *gorm.DB {
		return db.Order("auto_model_mappings.priority DESC")
	}).Find(&configs).Error

	return configs, err
}

// UpdateAutoModelConfig updates or creates an auto model configuration with its mappings
type UpdateAutoModelConfigRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
	Models      []string `json:"models" binding:"required,min=1"`
	UserID      uint     `json:"-"` // Not part of JSON, set from context
}

func UpdateAutoModelConfig(db *gorm.DB, req UpdateAutoModelConfigRequest) error {
	// Start a transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create or update the config
	config := AutoModelConfig{
		UserID:      req.UserID,
		Name:        req.Name,
		Description: req.Description,
		IsActive:    req.IsActive,
	}

	// Use FirstOrCreate to get the ID if it exists, including user_id in the condition
	if err := tx.Where("user_id = ? AND name = ?", req.UserID, req.Name).FirstOrCreate(&config).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update config fields
	config.Name = req.Name
	config.Description = req.Description
	config.IsActive = req.IsActive
	if err := tx.Save(&config).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete existing mappings
	if err := tx.Where("config_id = ?", config.ID).Delete(&AutoModelMapping{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create new mappings with priority based on order (first in list = highest priority)
	for i, modelName := range req.Models {
		mapping := AutoModelMapping{
			ConfigID:  config.ID,
			ModelName: modelName,
			Priority:  len(req.Models) - i, // First model gets highest priority
		}
		if err := tx.Create(&mapping).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// CreateAutoModelConfig creates a new auto model configuration with its mappings
func CreateAutoModelConfig(db *gorm.DB, config *AutoModelConfig) error {
	// Start a transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create the config
	if err := tx.Create(config).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create mappings with their priorities
	for i, mapping := range config.Mappings {
		mapping.ConfigID = config.ID
		mapping.Priority = len(config.Mappings) - i // First mapping gets highest priority
		if err := tx.Create(&mapping).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// DeleteAutoModelConfig deletes an auto model configuration and its mappings
func DeleteAutoModelConfig(db *gorm.DB, userID uint, name string) error {
	// Use a transaction to ensure data consistency
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// First find the config to get its ID, ensuring it belongs to the user
	var config AutoModelConfig
	if err := tx.Where("user_id = ? AND name = ?", userID, name).First(&config).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete all mappings for this config
	if err := tx.Where("config_id = ?", config.ID).Delete(&AutoModelMapping{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Finally, delete the config itself
	if err := tx.Delete(&config).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
